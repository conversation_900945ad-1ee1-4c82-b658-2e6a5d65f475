using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Databases;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Workers;

public class CleanupWorker : BackgroundService
{
    private readonly ILogger<CleanupWorker> _logger;
    private readonly IConfiguration _configuration;
    private ResourceService _resourceService;
    private readonly IServiceScopeFactory _factory;
    private readonly SystemConfigurationService _systemConfigurationService;
    private readonly RecordService _recordService;

    public CleanupWorker(IServiceScopeFactory factory, ILogger<CleanupWorker> logger, IConfiguration configuration,
        SystemConfigurationService systemConfigurationService, RecordService recordService)
    {
        _factory = factory;
        _logger = logger;
        _configuration = configuration;
        _systemConfigurationService = systemConfigurationService;
        _recordService = recordService;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var systemSetting = await _systemConfigurationService.GetSystemSettingAsync();
        if (!stoppingToken.IsCancellationRequested)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                using var scope = _factory.CreateScope();
                _resourceService = scope.ServiceProvider.GetService<ResourceService>();

                var availableGb = _configuration.GetValue<long?>("Cleanup:KeepAvailableGb");
                var availablePercentage = _configuration.GetValue<int?>("Cleanup:KeepAvailablePercentage");
                var path = systemSetting.RecordPath;

                // Check if storage monitoring is configured
                if (availableGb.HasValue || availablePercentage.HasValue)
                {
                    var info = _resourceService.GetStorageInfo(path);
                    var availableGbActual = info.available / (1024.0 * 1024.0 * 1024.0);
                    var availablePercentageActual = info.available * 100.0 / info.total;

                    _logger.LogInformation(
                        $"Storage monitoring: total={info.total / (1024.0 * 1024.0 * 1024.0):F2} GB, " +
                        $"available={availableGbActual:F2} GB ({availablePercentageActual:F1}%), " +
                        $"thresholds: KeepAvailableGb={availableGb}, KeepAvailablePercentage={availablePercentage}%");

                    // Check if storage is critically low
                    var shouldEmergencyCleanup = IsStorageCriticallyLow(info, availableGb, availablePercentage);

                    if (shouldEmergencyCleanup)
                    {
                        _logger.LogCritical("CRITICAL: Storage is critically low! Available: {AvailableGb:F2} GB ({AvailablePercent:F1}%). " +
                                          "Initiating emergency cleanup procedure...", availableGbActual, availablePercentageActual);
                        await PerformEmergencyCleanup(stoppingToken);
                    }
                    else
                    {
                        // Normal cleanup - only if needed
                        var shouldCleanup = ShouldPerformCleanup(info, availableGb, availablePercentage);
                        if (shouldCleanup)
                        {
                            _logger.LogWarning("Storage threshold reached. Available: {AvailableGb:F2} GB ({AvailablePercent:F1}%). " +
                                             "Performing normal cleanup...", availableGbActual, availablePercentageActual);
                            await PerformNormalCleanup(stoppingToken);
                        }
                        else
                        {
                            _logger.LogDebug("Storage levels are acceptable. Available: {AvailableGb:F2} GB ({AvailablePercent:F1}%). " +
                                           "No cleanup needed.", availableGbActual, availablePercentageActual);
                        }
                    }
                }
                else
                {
                    _logger.LogDebug("Storage monitoring not configured. Skipping cleanup checks.");
                }

                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation($"{nameof(CleanupWorker)} stopped");
        return base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// Determines if storage is critically low and requires emergency cleanup
    /// </summary>
    private bool IsStorageCriticallyLow((long total, long available) info, long? availableGb, int? availablePercentage)
    {
        // Critical thresholds are more aggressive than normal cleanup thresholds
        var criticalGbThreshold = availableGb.HasValue ? availableGb.Value * 0.5 : (long?)null; // 50% of configured GB threshold
        var criticalPercentageThreshold = availablePercentage.HasValue ? availablePercentage.Value * 0.5 : (int?)null; // 50% of configured percentage threshold

        var gbCritical = criticalGbThreshold.HasValue && info.available < criticalGbThreshold.Value * 1024 * 1024 * 1024;
        var percentageCritical = criticalPercentageThreshold.HasValue &&
                                info.available * 100.0 / info.total < criticalPercentageThreshold.Value;

        return gbCritical || percentageCritical;
    }

    /// <summary>
    /// Determines if normal cleanup should be performed
    /// </summary>
    private bool ShouldPerformCleanup((long total, long available) info, long? availableGb, int? availablePercentage)
    {
        var gbThresholdReached = availableGb.HasValue && info.available < availableGb.Value * 1024 * 1024 * 1024;
        var percentageThresholdReached = availablePercentage.HasValue &&
                                        info.available * 100.0 / info.total < availablePercentage.Value;

        return gbThresholdReached || percentageThresholdReached;
    }

    /// <summary>
    /// Performs emergency cleanup when storage is critically low
    /// Stops all pipelines, deletes all records, then restarts pipelines
    /// </summary>
    private async Task PerformEmergencyCleanup(CancellationToken stoppingToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            _logger.LogCritical("=== EMERGENCY CLEANUP PROCEDURE STARTED ===");

            // Step 1: Stop all pipelines
            _logger.LogCritical("Step 1/4: Stopping all recording pipelines to prevent data corruption...");
            var stopStartTime = System.Diagnostics.Stopwatch.StartNew();
            await _recordService.StopAllAsync(stoppingToken);
            stopStartTime.Stop();
            _logger.LogInformation("All pipelines stopped successfully in {ElapsedMs} ms.", stopStartTime.ElapsedMilliseconds);

            // Step 2: Delete all records
            _logger.LogCritical("Step 2/4: Deleting ALL recording files from storage...");
            var cleanupStartTime = System.Diagnostics.Stopwatch.StartNew();
            var deletedCount = await CleanupAllRecords();
            cleanupStartTime.Stop();
            _logger.LogCritical("Emergency cleanup completed. Deleted {DeletedCount} recording files in {ElapsedMs} ms.",
                              deletedCount, cleanupStartTime.ElapsedMilliseconds);

            // Step 3: Wait a moment for cleanup to complete
            _logger.LogInformation("Step 3/4: Waiting for file system cleanup to complete...");
            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

            // Step 4: Restart all pipelines
            _logger.LogCritical("Step 4/4: Restarting all recording pipelines...");
            var startStartTime = System.Diagnostics.Stopwatch.StartNew();
            await _recordService.StartAll();
            startStartTime.Stop();
            _logger.LogInformation("All pipelines restarted successfully in {ElapsedMs} ms.", startStartTime.ElapsedMilliseconds);

            stopwatch.Stop();
            _logger.LogCritical("=== EMERGENCY CLEANUP PROCEDURE COMPLETED SUCCESSFULLY in {TotalElapsedMs} ms ===",
                              stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogCritical(ex, "=== EMERGENCY CLEANUP PROCEDURE FAILED after {TotalElapsedMs} ms ===",
                              stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// Performs normal cleanup by removing oldest records until storage threshold is met
    /// </summary>
    private async Task PerformNormalCleanup(CancellationToken stoppingToken)
    {
        var retry = 0;
        var maxRetries = 20;

        while (retry < maxRetries && !stoppingToken.IsCancellationRequested)
        {
            var result = await CleanupOldestRecord();
            if (result)
            {
                _logger.LogInformation("Normal cleanup completed successfully.");
                break;
            }

            await Task.Delay(TimeSpan.FromSeconds(15), stoppingToken);
            retry++;
        }

        if (retry >= maxRetries)
        {
            _logger.LogWarning($"Normal cleanup reached maximum retry limit ({maxRetries})");
        }
    }

    /// <summary>
    /// Deletes ALL recording files from storage (used in emergency cleanup)
    /// </summary>
    private async Task<int> CleanupAllRecords()
    {
        var deletedCount = 0;
        try
        {
            using var scope = _factory.CreateScope();
            var db = scope.ServiceProvider.GetService<GatewayDbContext>();

            // Get ALL recording files, not just synchronized ones
            var recordingFiles = await db.RecordingFiles.AsNoTracking().ToListAsync();

            _logger.LogWarning($"Found {recordingFiles.Count} recording files to delete in emergency cleanup");

            foreach (var recordingFile in recordingFiles)
            {
                try
                {
                    if (File.Exists(recordingFile.Path))
                    {
                        File.Delete(recordingFile.Path);
                        _logger.LogInformation($"Deleted file: {recordingFile.Path}");
                    }

                    db.Remove(recordingFile);
                    deletedCount++;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, $"Failed to delete file: {recordingFile.Path}");
                }
            }

            await db.SaveChangesAsync();
            return deletedCount;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error during complete storage cleanup");
            return deletedCount;
        }
    }

    private async Task<bool> CleanupOldestRecord()
    {
        try
        {
            using var scope = _factory.CreateScope();
            var db = scope.ServiceProvider.GetService<GatewayDbContext>();
            var recordingFiles =
                await db.RecordingFiles.AsNoTracking().Where(f => f.IsSynchronized == true).ToListAsync();
            foreach (var recordingFile in recordingFiles)
            {
                try
                {
                    if (File.Exists(recordingFile.Path))
                    {
                        File.Delete(recordingFile.Path);
                        _logger.LogInformation($"Deleted file: {recordingFile.Path}");
                    }

                    db.Remove(recordingFile);
                    await db.SaveChangesAsync();
                }
                catch (Exception e)
                {
                    _logger.LogError(e, $"Failed to delete file: {recordingFile.Path}");
                }
            }

            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return false;
        }
    }
}